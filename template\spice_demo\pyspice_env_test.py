import PySpice.Logging.Logging as Logging
logger = Logging.setup_logging()

from PySpice.Spice.Netlist import Circuit
from PySpice.Unit import *

def test_pyspice_installation():
    """
    简单的电压分压器电路测试
    测试PySpice和ngspice环境是否正常安装
    """
    
    try:
        # 创建电路
        circuit = Circuit('Voltage Divider Test Circuit')
        
        # 添加电压源：5V直流电源
        circuit.V('input', 'vin', circuit.gnd, 5@u_V)
        
        # 添加电阻：R1=1kΩ, R2=2kΩ 组成分压器
        circuit.R('1', 'vin', 'vout', 1@u_kΩ)
        circuit.R('2', 'vout', circuit.gnd, 2@u_kΩ)
        
        print("电路创建成功！")
        print("电路网表：")
        print(circuit)
        
        # 创建仿真器并运行DC分析
        simulator = circuit.simulator(temperature=25, nominal_temperature=25)
        analysis = simulator.operating_point()
        
        print("\n=== DC分析结果 ===")
        
        # 获取节点电压
        input_voltage = float(analysis['vin'])
        output_voltage = float(analysis['vout'])
        
        print(f"输入电压 (vin): {input_voltage:.3f} V")
        print(f"输出电压 (vout): {output_voltage:.3f} V")
        
        # 验证分压公式：Vout = Vin * R2/(R1+R2) = 5V * 2kΩ/(1kΩ+2kΩ) = 3.333V
        expected_voltage = 5.0 * 2 / (1 + 2)
        print(f"理论输出电压: {expected_voltage:.3f} V")
        
        # 检查结果是否合理（允许小误差）
        if abs(output_voltage - expected_voltage) < 0.001:
            print("\n✅ 测试通过！PySpice和ngspice环境安装正常。")
            return True
        else:
            print(f"\n❌ 测试失败！电压不匹配，误差: {abs(output_voltage - expected_voltage):.6f} V")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请检查PySpice是否正确安装")
        return False
        
    except Exception as e:
        print(f"❌ 仿真错误: {e}")
        print("请检查ngspice是否正确安装并在PATH中")
        return False

def test_dc_sweep():
    """
    额外测试：DC扫描分析
    """
    try:
        circuit = Circuit('DC Sweep Test')
        
        # 可变电压源
        circuit.V('input', 'vin', circuit.gnd, 0@u_V)
        circuit.R('load', 'vin', circuit.gnd, 1@u_kΩ)
        
        simulator = circuit.simulator(temperature=25, nominal_temperature=25)
        
        # DC扫描：从0V到5V，步长0.5V
        analysis = simulator.dc(Vinput=slice(0, 5, 0.5))
        
        print("\n=== DC扫描测试 ===")
        print("电压扫描结果：")
        
        for voltage in analysis.vin:
            print(f"输入: {float(voltage):.1f}V -> 节点电压: {float(voltage):.1f}V")
            
        print("✅ DC扫描测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ DC扫描测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始PySpice环境测试...")
    print("=" * 50)
    
    # 基本测试
    basic_test = test_pyspice_installation()
    
    if basic_test:
        # 如果基本测试通过，进行额外测试
        advanced_test = test_dc_sweep()
        
        if advanced_test:
            print("\n🎉 所有测试通过！PySpice环境完全正常！")
        else:
            print("\n⚠️  基本功能正常，但高级功能可能有问题。")
    else:
        print("\n❌ 基本测试失败，请检查安装。")
        
    print("\n安装建议：")
    print("1. 安装PySpice: pip install PySpice")
    print("2. 安装ngspice: ")
    print("   - Windows: 下载ngspice二进制文件并添加到PATH")
    print("   - Linux: sudo apt-get install ngspice")
    print("   - macOS: brew install ngspice")