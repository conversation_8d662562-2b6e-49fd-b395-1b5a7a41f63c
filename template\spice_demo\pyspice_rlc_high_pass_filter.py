import os
import numpy as np
import matplotlib.pyplot as plt
from PySpice.Spice.Netlist import Circuit
from PySpice.Unit import *
import logging

# 创建输出目录
output_dir = 'output'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(output_dir, 'filter_design.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """RLC高通滤波器设计"""
    
    logger.info("Starting 10kHz high-pass filter design")
    
    # 设计参数
    fc = 10e3  # 10kHz
    L_value = 1e-3*1.4   # 1mH
    C_value = 1 / (4 * np.pi**2 * fc**2 * L_value)*0.41  # 计算电容值
    R_value = 1e3*1.1    # 1kΩ
    
    logger.info(f"Component values: L={L_value*1000:.1f}mH, C={C_value*1e9:.1f}nF, R={R_value/1000:.1f}kΩ")
    
    # 创建电路
    circuit = Circuit('High-Pass Filter')
    
    # 正确定义AC电压源（包含DC和AC成分）
    circuit.V('in', 'input', circuit.gnd, 'DC 0V AC 1V')
    
    # 使用数值而不是单位对象（避免单位警告）
    circuit.C('1', 'input', 'output', C_value)
    circuit.L('1', 'output', circuit.gnd, L_value)
    circuit.R('1', 'output', circuit.gnd, R_value)
    
    logger.info("Circuit created successfully")
    
    # 仿真
    simulator = circuit.simulator(temperature=25)
    analysis = simulator.ac(start_frequency=100, 
                           stop_frequency=100000, 
                           number_of_points=1000, 
                           variation='dec')
    # print(analysis['output'])
    
    # 提取数据
    frequency = np.array([float(f) for f in analysis.frequency])
    output_voltage = analysis['output']

    # 计算电路的电压增益,因为输入电压幅度是1，所以没有除以1，应该是 20 * np.log10( np.abs( output_voltage / input_voltage ) )
    magnitude_db = 20 * np.log10(np.abs(output_voltage))
    logger.info( f" analysis, type( analysis )  \n    {analysis}, {type( analysis )}" )
    logger.info( f"frequency , type( frequency ) \n    {frequency} , {type( frequency )}" )
    logger.info( f"output_voltage, type( output_voltage ) \n    {output_voltage}, {type( output_voltage )}"  )
    logger.info( f"np.array(output_voltage) \n    {np.array(output_voltage)}" )
    logger.info( f"np.array(output_voltage)[0] , type( np.array(output_voltage)[0] ) \n    {np.array(output_voltage)[0]} , {type( np.array(output_voltage)[0] )}" )
    logger.info( f"magnitude_db , type( magnitude_db ) \n    {magnitude_db} , {type( magnitude_db )}" )
    logger.info( f"magnitude_db[0] , type( magnitude_db[0] ) \n    {magnitude_db[0]} , {type( magnitude_db[0] )}" )
    
    # 找到-3dB点
    # np.argmin(...): 这个函数会返回数组中最小值的索引
    idx_3db = np.argmin(np.abs(magnitude_db + 3))
    f_3db = frequency[idx_3db]
    
    logger.info(f"Actual cutoff frequency: {f_3db/1000:.2f} kHz")
    
    # 绘图（使用英文）
    plt.figure(figsize=(12, 8))
    
    # 幅频特性
    plt.subplot(2, 1, 1)
    plt.semilogx(frequency, magnitude_db, 'b-', linewidth=2, label='Magnitude')
    plt.plot(f_3db, -3, 'ro', markersize=8, label=f'-3dB point: {f_3db/1000:.2f} kHz')
    plt.axhline(-3, color='r', linestyle='--', alpha=0.5)
    plt.grid(True, alpha=0.3)
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Magnitude (dB)')
    plt.title('RLC High-Pass Filter - Frequency Response')
    plt.legend()
    plt.ylim(-40, 5)
    
    # 相频特性
    plt.subplot(2, 1, 2)
    phase = np.angle(output_voltage) * 180 / np.pi
    plt.semilogx(frequency, phase, 'g-', linewidth=2, label='Phase')
    plt.grid(True, alpha=0.3)
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Phase (degrees)')
    plt.title('Phase Response')
    plt.legend()
    
    plt.tight_layout()
    
    # 保存图片
    plot_path = os.path.join(output_dir, 'frequency_response.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    logger.info(f"Plot saved: {plot_path}")
    
    plt.show()
    
    # 输出关键参数
    print(f"\n=== Filter Design Summary ===")
    print(f"Target cutoff frequency: {fc/1000:.1f} kHz")
    print(f"Actual cutoff frequency: {f_3db/1000:.2f} kHz")
    print(f"Error: {abs(f_3db-fc)/fc*100:.1f}%")
    print(f"Component values:")
    print(f"  L = {L_value*1000:.1f} mH")
    print(f"  C = {C_value*1e9:.1f} nF") 
    print(f"  R = {R_value/1000:.1f} kΩ")
    
    logger.info("Design completed successfully")

if __name__ == "__main__":
    main()