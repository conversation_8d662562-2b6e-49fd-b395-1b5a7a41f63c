from langchain_core.messages import AIMessage, HumanMessage
import os, getpass
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv
import pprint
from langgraph.graph import MessagesState
from langgraph.graph import StateGraph, START, END
# from IPython.display import Image, display



def test_001():
    # Start a mini conversation
    messages = [
        AIMessage(content="So you said you were researching ocean mammals?", name="Model"),
        HumanMessage(content="Yes, that's right.", name="<PERSON>"),
        # AIMessage(content="Great! What would you like to learn about?", name="Model")
    ]

    # Pretty-print each message
    for m in messages:
        m.pretty_print()

    llm = ChatOpenAI(
        model="google/gemini-2.5-pro",
        base_url="https://openrouter.ai/api/v1",
        api_key=os.environ["OPENROUTER_API_KEY"]
    )

    # Test invocation
    response = llm.invoke(messages)
    print("*" * 80)
    print(response.content)               # AIMessage(content=..., name=..., ...)
    # print(response.response_metadata)
    print("^"*80)
    print(response.__dict__.keys())


def test_002():
    def divide(a: float, b: float) -> float:
        """Divide a by b (raises if b == 0)."""
        if b == 0:
            raise ValueError("Cannot divide by zero.")
        return a / b

    llm = ChatOpenAI(
        model="google/gemini-2.5-pro",
        base_url="https://openrouter.ai/api/v1",
        api_key=os.environ["OPENROUTER_API_KEY"]
    )

    # Bind it so the LLM can call “divide”
    llm_with_tools = llm.bind_tools([divide])

    def step_llm(state: MessagesState) -> MessagesState:
        new_ai_msg = llm_with_tools.invoke(state["messages"])
        return {"messages": [new_ai_msg]}

    # Build & compile
    builder = StateGraph(MessagesState)
    builder.add_node("step_llm", step_llm)
    builder.add_edge(START, "step_llm")
    builder.add_edge("step_llm", END)
    graph = builder.compile()

    # Optional: visualize as Mermaid
    # display(Image(graph.get_graph().draw_mermaid_png())) 

    if 0:
        # 1) Simple chat
        output = graph.invoke({"messages": HumanMessage(content="Hello there!", name="User")})
        for msg in output["messages"]:
            msg.pretty_print()
        print("*"*100)
    else:
        # 2) Division request
        output = graph.invoke({"messages": HumanMessage(content="Please divide 20 by 5.", name="User")})
        for msg in output["messages"]:
            msg.pretty_print()       

if __name__ == "__main__":
    load_dotenv()
    print("test_001" + "*"*80)
    test_001()
    print("test_002" + "*"*80)
    test_002()