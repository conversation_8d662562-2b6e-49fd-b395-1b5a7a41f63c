# core_arc.py - 完整的电路AI自动化设计系统
# 使用LangGraph + ngspice + OpenRouter API实现

# ==============================================================================
# 导入所有必需的库
# ==============================================================================

from typing import Literal, Optional, List, Tuple, Any, TypedDict, Annotated
from pydantic import BaseModel, Field
import operator
from logging_manager import LoggingManager, handle_error
import numpy as np
import os
import uuid
import re
import sys
import json
import logging
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# LangChain and LangGraph imports
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START, END
from langgraph.graph import MessagesState

# PySpice imports
from PySpice.Spice.Netlist import Circuit
from PySpice.Unit import u_Hz, u_V

# ==============================================================================
# 日志记录系统已移至logging_manager.py
# ==============================================================================

# ==============================================================================
# 创建全局日志管理器实例
# ==============================================================================

# 创建全局日志管理器实例
logger = LoggingManager()

# ==============================================================================

# ==============================================================================
# 数据模型定义 (Pydantic Models)
# ==============================================================================

class DesignGoal(BaseModel):
    """
    结构化的电路设计目标，由PlannerAgent生成。
    """
    circuit_type: Literal['low_pass_rc', 'high_pass_rc', 'band_pass_rc'] = Field(
       ..., description="目标电路的类型, 例如 'low_pass_rc'."
    )
    target_metric: Literal['cutoff_frequency', 'gain', 'phase_margin'] = Field(
       ..., description="核心优化指标, 例如 'cutoff_frequency'."
    )
    target_value: float = Field(..., description="目标指标的数值。")
    target_unit: str = Field(..., description="目标指标的单位, 例如 'Hz', 'dB'。")
    tolerance: Optional[float] = Field(0.05, description="目标值的可接受公差范围, 例如 0.05 表示 ±5%。")

class CircuitComponent(BaseModel):
    """
    表示电路中的单个元件。
    """
    name: str = Field(..., description="元件的唯一标识符, 例如 'R1', 'C1'。")
    type: Literal['R', 'C', 'V', 'L'] = Field(..., description="元件类型 (Resistor, Capacitor, Voltage Source, Inductor)。")
    nodes: Tuple[str, str] = Field(..., description="元件连接的两个节点, 例如 ('in', 'out')。")
    value: float = Field(..., description="元件的数值 (单位在生成网表时指定)。")
    unit: str = Field(..., description="元件值的单位, 例如 'kOhm', 'nF', 'V'。")

class CircuitModel(BaseModel):
    """
    表示完整的电路模型，由CircuitDesignerAgent生成或修改。
    """
    title: str = Field("AI Generated Circuit", description="电路的标题。")
    components: List[CircuitComponent] = Field(..., description="构成电路的元件列表。")
    # 新增：PySpice电路对象
    pyspice_circuit: Optional[Any] = Field(None, description="PySpice电路对象，用于仿真。")
    
    class Config:
        arbitrary_types_allowed = True

class SimulationParameters(BaseModel):
    """
    定义仿真任务的参数。
    """
    analysis_type: Literal['ac', 'tran', 'op'] = Field(..., description="要执行的仿真分析类型。")
    
    # AC 分析参数
    start_frequency: Optional[float] = Field(..., description="AC分析的起始频率 (Hz)。")
    stop_frequency: Optional[float] = Field(..., description="AC分析的终止频率 (Hz)。")
    number_of_points: Optional[int] = Field(..., description="AC分析中每个十倍频的点数。")
    output_node: str = Field(..., description="AC分析中用于测量输出的节点名称。")

class SimulationResult(BaseModel):
    """
    封装单次仿真运行的结果，由SimulationAgent(Tool)返回。
    """
    analysis_type: str = Field(..., description="执行的仿真分析类型。")
    data: Optional[Any] = Field(None, description="仿真成功时返回的数据, 通常是包含NumPy数组的字典。")
    vectors: Optional[List[str]] = Field(None, description="数据中包含的向量名称列表, 例如 ['frequency', 'v(out)']。")
    error: Optional[str] = Field(None, description="仿真失败时返回的错误信息。")

    class Config:
        arbitrary_types_allowed = True

class Critique(BaseModel):
    """
    封装单次评估的结果，由CritiqueAgent生成。
    """
    goal_met: bool = Field(..., description="一个布尔标志，指示设计目标是否已在容差范围内实现。")
    feedback: str = Field(..., description="给设计智能体的简洁文本反馈，说明当前设计的问题和改进建议。")
    metrics: dict = Field(..., description="一个包含从仿真结果中计算出的关键性能指标的字典。")

class AppState(TypedDict):
    """
    定义LangGraph的中心状态 (State)。
    这是整个应用中所有智能体共享的唯一事实来源。
    """
    user_request: str
    design_goal: Optional[DesignGoal]
    circuit_model: Optional[CircuitModel]
    simulation_params: Optional[SimulationParameters]
    simulation_results: Optional[SimulationResult]
    critique_history: Annotated[List[Critique], operator.add]
    iteration_count: int
    llm_format_prompt: str

# ==============================================================================
# NgSpice仿真器工具类
# ==============================================================================
def get_unit_multiplier(unit: str) -> float:
    """获取单位的乘数"""
    unit_map = {
        'k': 1e3, 'K': 1e3, 'kOhm': 1e3, 'kohm': 1e3,
        'm': 1e-3, 'mF': 1e-3, 'mH': 1e-3,
        'u': 1e-6, 'uF': 1e-6, 'µF': 1e-6, 'uH': 1e-6,
        'n': 1e-9, 'nF': 1e-9, 'nH': 1e-9,
        'p': 1e-12, 'pF': 1e-12, 'pH': 1e-12,
        'Ohm': 1.0, 'ohm': 1.0, 'F': 1.0, 'H': 1.0, 'V': 1.0
    }
    lowercase_unit_map = {key.lower(): value for key, value in unit_map.items()}
    try:
        value = lowercase_unit_map[unit.lower()]
    except Exception as e:
        handle_error( e, f"get_unit_multiplier error:{unit}", exit_on_error=True )
    
    # return lowercase_unit_map.get(unit.lower(), 1.0)
    return value

class NgspiceSimulator:
    """
    纯粹的仿真执行器，只负责运行已经创建好的电路。
    """

    def __init__(self):
        """初始化仿真器"""
        pass

    def run_ac_analysis(
        self,
        circuit_model: CircuitModel,
        params: SimulationParameters
    ) -> SimulationResult:
        """
        执行交流（AC）分析。
        """
        try:
            if not circuit_model.pyspice_circuit:
                raise ValueError("No PySpice circuit object found in circuit model")
            
            print(f"🔬 开始AC分析 (频率范围: {params.start_frequency} - {params.stop_frequency} Hz)")
            
            return self._run_pyspice_ac_analysis(circuit_model.pyspice_circuit, params)
                
        except Exception as e:
            handle_error(e, "AC Analysis Simulation",exit_on_error=True)            
            # return SimulationResult(
            #     analysis_type='ac',
            #     data=None,
            #     vectors=None,
            #     error=f"Simulation failed: {str(e)}"
            # )

    def _run_pyspice_ac_analysis(self, circuit, params: SimulationParameters) -> SimulationResult:
        """
        使用PySpice执行AC分析。
        """
        try:
            # Run AC analysis using PySpice unit system
            simulator = circuit.simulator(temperature=25, nominal_temperature=25)
            analysis = simulator.ac(
                start_frequency=float(params.start_frequency),
                stop_frequency=float(params.stop_frequency),
                number_of_points=int(params.number_of_points),
                variation='dec'
            )
            
            # Extract results
            frequency = np.array(analysis.frequency)
            
            # 获取输出节点的电压
            output_node = params.output_node
            try:
                output_voltage = np.array(analysis[output_node])
            except KeyError:
                # 尝试添加v()前缀
                handle_error(e, "_run_pyspice_ac_analysis KeyError", exit_on_error=True)
                try:
                    output_voltage = np.array(analysis[f"v({output_node})"])
                except KeyError:
                    available_nodes = list(analysis.nodes.keys())
                    raise KeyError(f"Output node '{output_node}' not found. Available nodes: {available_nodes}")
            
            data = {
                'frequency': frequency,
                'output_voltage': output_voltage
            }
            
            print(f"✅ PySpice仿真完成 (数据点: {len(frequency)})")
            
            return SimulationResult(
                analysis_type='ac',
                data=data,
                vectors=['frequency', 'output_voltage'],
                error=None
            )
            
        except Exception as e:
            # raise RuntimeError(f"PySpice AC analysis failed: {str(e)}")
            handle_error(e, f"PySpice AC analysis failed: {str(e)}", exit_on_error=True)

# ==============================================================================
# 电路创建工具函数
# ==============================================================================

def create_pyspice_circuit(circuit_model: CircuitModel):
    """
    根据CircuitModel创建PySpice电路对象。
    这个函数将在circuit_designer_node中被调用。
    """        
    try:
        print(f"🔧 创建PySpice电路: {circuit_model.title}")
        
        # Create PySpice circuit
        circuit = Circuit(circuit_model.title)
        
        # Add components based on circuit_model
        for component in circuit_model.components:
            if component.type == 'V':
                # 添加电压源 (简化版本，不使用ac_value参数)
                value = component.value * get_unit_multiplier(component.unit)
                circuit.V(
                    component.name[1:], 
                    component.nodes[0], 
                    circuit.gnd if component.nodes[1] == '0' else component.nodes[1], 
                    component.value
                )
                # print(f"  + 电压源 {component.name}: {component.value}V")
                print(f"  + 电阻 {component.name}: {component.value}{component.unit} ({value}V)")
            elif component.type == 'R':
                value = component.value * get_unit_multiplier(component.unit)
                circuit.R(
                    component.name[1:], 
                    component.nodes[0], 
                    component.nodes[1], 
                    value
                )
                print(f"  + 电阻 {component.name}: {component.value}{component.unit} ({value}Ω)")
            elif component.type == 'C':
                value = component.value * get_unit_multiplier(component.unit)
                circuit.C(
                    component.name[1:], 
                    component.nodes[0], 
                    component.nodes[1], 
                    value
                )
                print(f"  + 电容 {component.name}: {component.value}{component.unit} ({value}F)")
            elif component.type == 'L':
                value = component.value * get_unit_multiplier(component.unit)
                circuit.L(
                    component.name[1:], 
                    component.nodes[0], 
                    component.nodes[1], 
                    value
                )
                print(f"  + 电感 {component.name}: {component.value}{component.unit} ({value}H)")
        
        print("✅ PySpice电路对象创建成功")
        return circuit
        
    except Exception as e:
        handle_error(e, "PySpice Circuit Creation", exit_on_error=True)
        return None



# ==============================================================================
# 智能体节点实现
# ==============================================================================

# 全局LLM实例
def get_llm():
    """获取LLM实例"""
    try:
        api_key = os.environ.get("OPENROUTER_API_KEY")
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY environment variable not found")
        
        return ChatOpenAI(
            model="anthropic/claude-sonnet-4",
            base_url="https://openrouter.ai/api/v1",
            api_key=api_key,
            temperature=0.7
        )
    except Exception as e:
        handle_error(e, "LLM Initialization", exit_on_error=True)

# ==============================================================================
# 分析函数 (确定性计算)
# ==============================================================================

def calculate_cutoff_frequency(freq_array: np.ndarray, vout_array: np.ndarray) -> float:
    """
    从AC分析结果中计算-3dB截止频率。
    """
    try:
        # 计算幅度响应 (dB)
        magnitude_db = 20 * np.log10(np.abs(vout_array))
        
        # 找到最大值
        max_db = np.max(magnitude_db)
        
        # 找到-3dB点
        target_db = max_db - 3
        
        # 找到最接近-3dB点的频率
        idx = np.argmin(np.abs(magnitude_db - target_db))
        cutoff_freq = freq_array[idx]
        
        print(f"📊 计算截止频率: {cutoff_freq:.2f} Hz (最大增益: {max_db:.2f} dB)")
        
        return float(cutoff_freq)
    except Exception as e:
        handle_error(e, "Cutoff Frequency Calculation",exit_on_error=True)
        return 0.0

def calculate_gain_at_frequency(freq_array: np.ndarray, vout_array: np.ndarray, target_freq: float) -> float:
    """
    计算指定频率处的增益。
    """
    try:
        # 找到最接近目标频率的索引
        idx = np.argmin(np.abs(freq_array - target_freq))
        gain_db = 20 * np.log10(np.abs(vout_array[idx]))
        
        print(f"📊 在 {target_freq} Hz 处的增益: {gain_db:.2f} dB")
        
        return float(gain_db)
    except Exception as e:
        handle_error(e, "Gain Calculation", exit_on_error=True)
        return 0.0

# ==============================================================================
# PlannerAgent 节点
# ==============================================================================

def planner_node(state: AppState) -> dict:
    """
    PlannerAgent节点：将用户请求转换为结构化的设计目标。
    """
    print("\n"*2)
    print("*"*120 + "\n")
    print("planner_node\n")
    print("*"*120 + "\n")
    try:
        print("🎯 PlannerAgent: 分析用户需求...")
        llm = get_llm()

        system_prompt = state.get('llm_format_prompt', '') + """
你是一个电路设计规划专家。你的任务是分析用户的自然语言请求，并将其转换为结构化的电路设计目标。
用户可能会请求设计各种类型的电路，如：
- 低通RLC滤波器
- 高通RLC滤波器  
- 带通RLC滤波器

请从用户请求中提取：
1. circuit_type: 'low_pass_rc', 'high_pass_rc', 或 'band_pass_rc'
2. target_metric: 'cutoff_frequency', 'gain', 或 'phase_margin'
3. target_value: 数值
4. target_unit: 单位 (如 'Hz', 'kHz', 'MHz', 'dB')
5. tolerance: 容差 (默认0.05即5%)

返回两个JSON对象
一下是参考格式，不是要你照抄这个参数

1. 设计目标：
{
  "circuit_type": "low_pass_rc",
  "target_metric": "cutoff_frequency", 
  "target_value": 1000,
  "target_unit": "Hz",
  "tolerance": 0.05
}

2. 仿真参数：
{
  "analysis_type": "ac",
  "start_frequency": 100.0,
  "stop_frequency": 100000.0,
  "number_of_points": 1000,
  "output_node": "output"
}
"""
        user_message = f"用户请求: {state['user_request']}"     
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_message)
        ]
        
        # 记录完整的消息列表和元数据
        llm_request_data = {
            "messages": [
                {
                    "role": msg.type,
                    "content": msg.content
                } for msg in messages
            ],
            "model": "anthropic/claude-sonnet-4"
        }
        
        response = llm.invoke(messages)
        response_text = response.content
        
        # 记录LLM响应
        llm_response_data = {
            "response_text": response_text,
            "response_length": len(response_text)
        }
        
        # 记录完整的LLM交互
        logger.log_llm_interaction("planner_llm", {
            "request": llm_request_data,
            "response": llm_response_data
        })
        
        print(f"🤖 LLM响应长度: {len(response_text)} 字符")
        
        # 解析响应中的JSON
        try:
            # 提取设计目标JSON
            design_goal_match = re.search(r'\{[^}]*"circuit_type"[^}]*\}', response_text)
            if design_goal_match:
                design_goal_data = json.loads(design_goal_match.group())
                design_goal = DesignGoal(**design_goal_data)
                print(f"✅ 解析设计目标: {design_goal.circuit_type} - {design_goal.target_value}{design_goal.target_unit}")
            else:
                # raise ValueError("Could not extract design goal from LLM response")
                handle_error(e, "Could not extract design goal from LLM response", exit_on_error=True)
            
            # 提取仿真参数JSON
            sim_params_match = re.search(r'\{[^}]*"analysis_type"[^}]*\}', response_text)
            if sim_params_match:
                sim_params_data = json.loads(sim_params_match.group())
                simulation_params = SimulationParameters(**sim_params_data)
                print(f"✅ 解析仿真参数: {simulation_params.analysis_type} 分析")
            else:
                # raise ValueError("Could not extract simulation parameters from LLM response")
                handle_error(e, "Could not extract simulation parameters from LLM response", exit_on_error=True)
                
        except Exception as e:
            handle_error(e, "Parsing Planner Response", exit_on_error=True)

        return {
            'design_goal': design_goal,
            'simulation_params': simulation_params
        }
        
    except Exception as e:
        handle_error(e, "Planner Node", exit_on_error=True)

# ==============================================================================
# CircuitDesignerAgent 节点  
# ==============================================================================

def circuit_designer_node(state: AppState) -> dict:
    """
    CircuitDesignerAgent节点：根据设计目标和历史反馈生成/修改电路模型，
    并创建对应的PySpice电路对象。
    """
    print("\n"*2)
    print("*"*120 + "\n")    
    print("circuit_designer_node\n")
    print("*"*120 + "\n")    
    try:
        current_iteration = state.get('iteration_count', 0)
        print(f"🔧 CircuitDesignerAgent: 设计电路 (迭代 {current_iteration + 1})...")
        
        llm = get_llm()
        
        design_goal = state['design_goal']
        critique_history = state.get('critique_history', [])
        
        if not design_goal:
            # raise ValueError("No design goal available for circuit design")
            handle_error(e, "No design goal available for circuit design", exit_on_error=True)
        
        system_prompt = state.get('llm_format_prompt', '') + """
你是一个专业的电路设计工程师。你的任务是根据设计目标和反馈历史来设计或改进电路。

支持的电路类型：
1. low_pass_rc: RLC低通滤波器
2. high_pass_rc: RLC高通滤波器  
3. band_pass_rc: RLC带通滤波器

你必须返回一个包含电路标题和元件列表的JSON对象
一下是参考的电路设计格式，不是要你把电路设计成这样,components数量和参数根据设计目标设计

{
  "title": "电路标题",
  "components": [
    {
      "name": "V1",
      "type": "V", 
      "nodes": ["in", "0"],
      "value": 1.0,
      "unit": "V"
    },
    {
      "name": "R1",
      "type": "R",
      "nodes": ["in", "out"], 
      "value": 1000,
      "unit": "Ohm"
    },
    {
      "name": "C1", 
      "type": "C",
      "nodes": ["out", "0"],
      "value": 159,
      "unit": "nF"
    }
  ]
}

如果有历史反馈，根据反馈调整设计。仅返回JSON格式的电路设计，不要添加其他解释。
"""

        # 构建用户消息
        user_message = f"""
设计目标：
- 电路类型: {design_goal.circuit_type}
- 目标指标: {design_goal.target_metric}
- 目标值: {design_goal.target_value} {design_goal.target_unit}
- 容差: ±{design_goal.tolerance*100}%

当前迭代: {current_iteration + 1}

你可以参考下面的设计参数
# 设计参数
fc = 10e3  # 10kHz
L_value = 1e-3*1.4   # 1mH
C_value = 1 / (4 * np.pi**2 * fc**2 * L_value)*0.41  # 计算电容值
R_value = 1e3*1.1    # 1kΩ
# 创建电路
circuit = Circuit('High-Pass Filter')
# 正确定义AC电压源（包含DC和AC成分）
circuit.V('in', 'input', circuit.gnd, 'DC 0V AC 1V')
# 使用数值而不是单位对象（避免单位警告）
circuit.C('1', 'input', 'output', C_value)
circuit.L('1', 'output', circuit.gnd, L_value)
circuit.R('1', 'output', circuit.gnd, R_value)
"""

        if critique_history:
            user_message += "\n历史反馈:\n"
            for i, critique in enumerate(critique_history[-3:]):  # 只显示最近3次反馈
                user_message += f"{i+1}. {critique.feedback}\n"
                user_message += f"   指标: {critique.metrics}\n"

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_message)
        ]
        
        # 记录LLM交互
        llm_request_data = {
            "messages": [
                {
                    "role": msg.type,
                    "content": msg.content
                } for msg in messages
            ],
            "iteration": current_iteration + 1,
            "has_feedback": len(critique_history) > 0
        }
        
        response = llm.invoke(messages)
        response_text = response.content
        
        llm_response_data = {
            "response_text": response_text,
            "response_length": len(response_text)
        }
        
        logger.log_llm_interaction("designer_llm", {
            "request": llm_request_data,
            "response": llm_response_data
        })
        
        print(f"🤖 设计响应长度: {len(response_text)} 字符")
        
        # 解析响应中的JSON
        try:
            # 首先尝试清理和标准化文本
            # 1. 移除可能的多余转义字符
            cleaned_text = response_text.replace('\\"', '"')
            # 2. 找到第一个完整的JSON对象
            circuit_match = re.search(r'\{[^{]*"title"[^}]*"components"\s*:\s*\[(.*?)\]\s*\}', cleaned_text, re.DOTALL)
            
            if circuit_match:
                try:
                    circuit_data = json.loads(circuit_match.group())
                except json.JSONDecodeError:
                    handle_error(e, "Parsing Designer json.JSONDecodeError", exit_on_error=True)
                circuit_model = CircuitModel(**circuit_data)
                print(f"✅ 解析电路设计: {circuit_model.title} ({len(circuit_model.components)} 个元件)")
            else:
                # raise ValueError("Could not extract circuit design from LLM response")
                handle_error(e, "Could not extract circuit design from LLM response", exit_on_error=True)
                            
        except Exception as e:
            handle_error(e, "Parsing Designer Response", exit_on_error=True)
        
        # 关键：在这里创建PySpice电路对象
        pyspice_circuit = create_pyspice_circuit(circuit_model)
        circuit_model.pyspice_circuit = pyspice_circuit
        
        # 保存电路模型到文件
        logger.save_circuit_model(circuit_model, current_iteration + 1)
        
        return {
            'circuit_model': circuit_model,
            'iteration_count': current_iteration + 1
        }
        
    except Exception as e:
        handle_error(e, "Circuit Designer Node", exit_on_error=True)

# ==============================================================================
# SimulationAgent 节点
# ==============================================================================

# 全局仿真器实例
simulator_tool = NgspiceSimulator()

def simulation_node(state: AppState) -> dict:
    """
    SimulationAgent节点：执行电路仿真。
    """
    print("\n"*2)
    print("*"*120 + "\n")      
    print("simulation_node\n")
    print("*"*120 + "\n")      
    try:
        current_iteration = state.get('iteration_count', 0)
        print(f"⚡ SimulationAgent: 执行仿真 (迭代 {current_iteration})...")
        
        circuit_model = state['circuit_model']
        simulation_params = state['simulation_params']
        
        if not circuit_model:
            # raise ValueError("No circuit model available for simulation")  
            handle_error(e, "No circuit model available for simulation", exit_on_error=True)          
        if not simulation_params:
            # raise ValueError("No simulation parameters available")
            handle_error(e, "No simulation parameters available", exit_on_error=True)
        
        # 执行仿真
        if simulation_params.analysis_type == 'ac':
            result = simulator_tool.run_ac_analysis(circuit_model, simulation_params)
        else:
            handle_error(e, f"Unsupported analysis type: {simulation_params.analysis_type}", exit_on_error=True)
        
        if result.error:
            # print(f"❌ 仿真失败: {result.error}")
            handle_error(e, f"❌ 仿真失败: {result.error}", exit_on_error=True)
        else:
            print("✅ 仿真完成")
        
        # 保存仿真结果到文件
        logger.save_simulation_results(result, current_iteration)
        
        return {'simulation_results': result}
        
    except Exception as e:
        handle_error(e, "Simulation Node", exit_on_error=True)

# ==============================================================================
# CritiqueAgent 节点
# ==============================================================================

def critique_node(state: AppState) -> dict:
    """
    CritiqueAgent节点：评估仿真结果并提供反馈。
    """
    print("\n"*2)
    print("*"*120 + "\n")     
    print("critique_node\n")
    print("*"*120 + "\n")     
    try:
        current_iteration = state.get('iteration_count', 0)
        print(f"📊 CritiqueAgent: 评估结果 (迭代 {current_iteration})...")
        
        llm = get_llm()
        
        design_goal = state['design_goal']
        simulation_results = state['simulation_results']
        
        if not design_goal:
            # raise ValueError("No design goal available for critique")
            handle_error(e, "No design goal available for critique", exit_on_error=True)
        
        if not simulation_results or simulation_results.error:
            # 仿真失败的情况
            print(f"❌ 仿真失败，无法进行评估")
            critique = Critique(
                goal_met=False,
                feedback=f"仿真失败: {simulation_results.error if simulation_results else 'No simulation results'}",
                metrics={}
            )
            return {'critique_history': [critique]}
        
        # 从仿真结果计算性能指标
        try:
            freq_data = simulation_results.data['frequency']
            vout_data = simulation_results.data['output_voltage']
            
            print(f"📈 分析频响数据 ({len(freq_data)} 个数据点)")
            
            metrics = {}
            
            if design_goal.target_metric == 'cutoff_frequency':
                actual_cutoff = calculate_cutoff_frequency(freq_data, vout_data)
                metrics['cutoff_frequency'] = actual_cutoff
                metrics['target_cutoff'] = design_goal.target_value
                
                # 检查是否达到目标
                error_ratio = abs(actual_cutoff - design_goal.target_value) / design_goal.target_value
                goal_met = error_ratio <= design_goal.tolerance
                
                print(f"🎯 目标截止频率: {design_goal.target_value} Hz")
                print(f"📊 实际截止频率: {actual_cutoff:.2f} Hz")
                print(f"📉 误差率: {error_ratio*100:.1f}% (容差: ±{design_goal.tolerance*100}%)")
                print(f"✅ 目标{'达成' if goal_met else '未达成'}")
                
            elif design_goal.target_metric == 'gain':
                actual_gain = calculate_gain_at_frequency(freq_data, vout_data, design_goal.target_value)
                metrics['gain_at_freq'] = actual_gain
                metrics['target_gain'] = design_goal.target_value
                
                # 简化的目标检查
                goal_met = abs(actual_gain) <= 3  # 假设目标是不超过-3dB
                print(f"📊 在 {design_goal.target_value} Hz 处的增益: {actual_gain:.2f} dB")
                print(f"✅ 目标{'达成' if goal_met else '未达成'}")
            else:
                goal_met = False
                metrics['error'] = f"Unsupported metric: {design_goal.target_metric}"
                print(f"❌ 不支持的指标: {design_goal.target_metric}")
                
        except Exception as e:
            handle_error(e, "Performance Metrics Calculation", exit_on_error=True)
            goal_met = False
            metrics = {'error': str(e)}
        
        # 使用LLM生成详细反馈
        system_prompt = state.get('llm_format_prompt', '') + """
你是一个电路分析专家。根据仿真结果和设计目标，提供简洁的改进建议。

分析要点：
1. 比较实际性能指标与目标值
2. 如果未达到目标，建议具体的元件值调整
3. 对于RC滤波器，记住 fc = 1/(2πRC)

请返回一个JSON格式的评估结果，包含以下字段：
{
  "goal_met": false,
  "feedback": "当前截止频率为XXX Hz，目标为XXX Hz。建议将电阻值调整为XXX Ohm或电容值调整为XXX nF。",
  "metrics": {
    "actual_value": 1000,
    "target_value": 2000,
    "deviation": 0.5
  }
}

仅返回JSON格式的评估结果，不要添加其他解释文本。
"""

        user_message = f"""
设计目标: {design_goal.target_metric} = {design_goal.target_value} {design_goal.target_unit}
实际指标: {metrics}
目标达成: {goal_met}

请提供改进建议。"""

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_message)
        ]
        
        # 记录LLM交互
        llm_request_data = {
            "messages": [
                {
                    "role": msg.type,
                    "content": msg.content
                } for msg in messages
            ],
            "metrics": metrics,
            "goal_met": goal_met,
            "iteration": current_iteration
        }
        
        try:
            response = llm.invoke(messages)
            feedback = response.content
            
            llm_response_data = {
                "feedback": feedback,
                "feedback_length": len(feedback)
            }
            
            logger.log_llm_interaction("critique_llm", {
                "request": llm_request_data,
                "response": llm_response_data
            })
            
            print(f"🤖 反馈长度: {len(feedback)} 字符")
            
        except Exception as e:
            handle_error(e, "LLM Feedback Generation",exit_on_error=True)
        
        critique = Critique(
            goal_met=goal_met,
            feedback=feedback,
            metrics=metrics
        )
        
        print(f"📝 反馈: {feedback[:100]}...")
        
        return {'critique_history': [critique]}
        
    except Exception as e:
        handle_error(e, "Critique Node", exit_on_error=True)

# ==============================================================================
# 图构建和路由逻辑
# ==============================================================================

MAX_ITERATIONS = 10

def after_critique_router(state: AppState) -> str:
    """
    在Critique节点后决定下一步。
    """
    critique_history = state.get('critique_history', [])
    iteration_count = state.get('iteration_count', 0)
    
    if not critique_history:
        print("🔄 没有评估历史，继续设计")
        return "designer"
    
    latest_critique = critique_history[-1]
    
    # 检查是否达到目标或最大迭代次数
    if latest_critique.goal_met:
        print("🎉 目标已达成，结束设计流程")
        return END
    elif iteration_count >= MAX_ITERATIONS:
        print(f"⏰ 已达到最大迭代次数 ({MAX_ITERATIONS})，结束设计流程")
        return END
    else:
        print(f"🔄 目标未达成，继续优化 (迭代 {iteration_count}/{MAX_ITERATIONS})")
        return "designer"

def after_simulator_router(state: AppState) -> str:
    """
    在Simulator节点后决定下一步。  
    """
    simulation_results = state.get('simulation_results')
    
    if not simulation_results:
        print("❌ 无仿真结果，返回设计器")
        return "designer"
    
    # 如果仿真失败，返回设计器修正
    if simulation_results.error:
        print(f"❌ 仿真失败，返回设计器修正: {simulation_results.error}")
        return "designer"
    else:
        print("✅ 仿真成功，进行评估")
        return "critique"

def build_graph():
    """
    构建、连接并编译LangGraph状态机。
    """
    try:
        print("📊 构建LangGraph状态机...")
        
        # 创建图
        workflow = StateGraph(AppState)
        
        # 添加节点
        workflow.add_node("planner", planner_node)
        workflow.add_node("designer", circuit_designer_node)
        workflow.add_node("simulator", simulation_node)
        workflow.add_node("critique", critique_node)
        
        # 添加边
        workflow.add_edge(START, "planner")
        workflow.add_edge("planner", "designer")
        workflow.add_edge("designer", "simulator")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "simulator",
            after_simulator_router,
            {
                "designer": "designer",
                "critique": "critique"
            }
        )
        
        workflow.add_conditional_edges(
            "critique", 
            after_critique_router,
            {
                "designer": "designer",
                END: END
            }
        )
        
        # 编译图
        app = workflow.compile()
        print("✅ LangGraph状态机构建完成")
        return app
        
    except Exception as e:
        handle_error(e, "Graph Building", exit_on_error=True)

# 定义全局常量
DEFAULT_LLM_FORMAT_PROMPT = ""
# DEFAULT_LLM_FORMAT_PROMPT = """

# 请以下面的方式输出JSON，不要使用转义字符，确保JSON格式正确且直接可用：
# {
#   "key": "value",
#   "array": [
#     {
#       "nested_key": "value"
#     }
#   ]
# }
# 注意：
# 1. 不要使用 \\" 这样的转义引号
# 2. 使用标准的JSON格式，key和string都用双引号
# 3. 不要添加任何额外的解释文本，只返回JSON

# """

# 编译应用
app = build_graph()

# ==============================================================================
# 主程序入口
# ==============================================================================

def main():
    """
    主应用函数，处理用户交互和图的执行。
    """
    try:
        print("=" * 80)
        print("🔋 电路AI自动化设计系统")
        print("=" * 80)
        print("支持的电路类型:")
        print("- RC低通滤波器")
        print("- RC高通滤波器") 
        print("- RC带通滤波器")
        print()
        print("示例请求:")
        print("- '设计一个截止频率为1kHz的RC低通滤波器'")
        print("- '我需要一个高通滤波器，截止频率为10kHz'")
        print("=" * 80)
        print(f"📁 输出目录: {logger.session_dir}")
        print(f"📋 日志目录: {logger.dirs['logs']}")
        print("=" * 80)
        
        while True:
            try:
                user_input = input("\n请输入您的电路设计需求 (输入 'quit' 退出): ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    print("⚠️  请输入有效的设计需求。")
                    continue
                
                # 生成唯一的线程ID
                thread_id = str(uuid.uuid4())
                
                # 初始化状态
                initial_state = AppState(
                    user_request=user_input,
                    design_goal=None,
                    circuit_model=None,
                    simulation_params=None,
                    simulation_results=None,
                    critique_history=[],
                    iteration_count=0,
                    llm_format_prompt=DEFAULT_LLM_FORMAT_PROMPT
                )
                
                print(f"\n🚀 开始处理您的请求...")
                print(f"📋 用户需求: {user_input}")
                print(f"🆔 会话ID: {thread_id}")
                
                start_time = datetime.now()
                
                # 执行图
                config = {"configurable": {"thread_id": thread_id}}
                final_state = app.invoke(initial_state, config)
                
                end_time = datetime.now()
                total_time = (end_time - start_time).total_seconds()
                
                # 保存最终结果
                results_file = logger.save_final_results(final_state)
                
                # 显示结果
                print("\n" + "=" * 60)
                print("🎯 设计结果")
                print("=" * 60)
                
                if final_state.get('design_goal'):
                    goal = final_state['design_goal']
                    print(f"📐 设计目标: {goal.circuit_type}")
                    print(f"🎯 目标指标: {goal.target_metric} = {goal.target_value} {goal.target_unit}")
                
                if final_state.get('circuit_model'):
                    circuit = final_state['circuit_model']
                    print(f"\n🔧 最终电路设计: {circuit.title}")
                    print("元件清单:")
                    for comp in circuit.components:
                        print(f"  - {comp.name}: {comp.type}, {comp.value}{comp.unit}, 节点: {comp.nodes}")
                    
                    # 显示PySpice电路状态
                    if circuit.pyspice_circuit:
                        print("✅ PySpice电路对象已创建")
                    else:
                        print("⚠️  PySpice电路对象创建失败或不可用")
                
                if final_state.get('critique_history'):
                    latest_critique = final_state['critique_history'][-1]
                    print(f"\n📊 性能评估:")
                    print(f"目标达成: {'✅ 是' if latest_critique.goal_met else '❌ 否'}")
                    print(f"指标: {latest_critique.metrics}")
                    print(f"反馈: {latest_critique.feedback}")
                
                print(f"\n📈 执行统计:")
                print(f"🔄 总迭代次数: {final_state.get('iteration_count', 0)}")
                print(f"⏱️  总执行时间: {total_time:.2f} 秒")
                print(f"📄 结果已保存到: {results_file}")
                print(f"📋 详细日志位于: {logger.dirs['logs']}")
                
            except KeyboardInterrupt:
                print("\n\n⚠️  用户中断，退出程序。")
                break
            except Exception as e:
                handle_error(e, "Main Loop")
                print("⚠️  请重试或检查您的输入。")
                
    except Exception as e:
        handle_error(e, "Main Function", exit_on_error=True)

if __name__ == "__main__":
    try:
        # 加载环境变量
        load_dotenv()
        
        # 检查API密钥
        if not os.environ.get("OPENROUTER_API_KEY"):
            print("❌ 错误: 未找到 OPENROUTER_API_KEY 环境变量")
            print("请在 .env 文件中设置您的 OpenRouter API 密钥")
            sys.exit(1)
        
        print("🚀 启动电路AI自动化设计系统...")
        main()
        
    except Exception as e:
        handle_error(e, "Application Startup", exit_on_error=True)
    finally:
        # 确保正确关闭日志系统
        if 'logger' in globals():
            logger.close()
        print("📋 日志系统已关闭")