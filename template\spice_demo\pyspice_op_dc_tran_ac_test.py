import numpy as np
import matplotlib.pyplot as plt
from PySpice.Spice.Netlist import Circuit
from PySpice.Unit import *
import os
import shutil
import sys
import pandas as pd
from datetime import datetime

# 目录管理
def setup_output_directory():
    """创建并清空output目录"""
    output_dir = "output"
    
    # 如果目录存在，先删除
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
        print(f"Cleared existing {output_dir} directory")
    
    # 创建新目录
    os.makedirs(output_dir)
    print(f"Created {output_dir} directory")
    
    return output_dir

# 日志管理类
class Logger:
    def __init__(self, output_dir):
        self.output_dir = output_dir
        self.log_file = os.path.join(output_dir, "simulation_log.txt")
        self.data_file = os.path.join(output_dir, "simulation_data.txt")
        
        # 创建日志文件
        with open(self.log_file, 'w') as f:
            f.write(f"PySpice Simulation Log - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*60 + "\n\n")
        
        # 创建数据文件
        with open(self.data_file, 'w') as f:
            f.write(f"PySpice Simulation Data - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*60 + "\n\n")
    
    def log(self, message):
        """同时输出到控制台和日志文件"""
        print(message)
        with open(self.log_file, 'a') as f:
            f.write(message + "\n")
    
    def save_data(self, title, data_dict):
        """保存仿真数据到文件"""
        with open(self.data_file, 'a') as f:
            f.write(f"\n{title}\n")
            f.write("-" * len(title) + "\n")
            for key, value in data_dict.items():
                f.write(f"{key}: {value}\n")
            f.write("\n")

# Create the circuit - RC Low Pass Filter
def create_rc_filter_circuit():
    circuit = Circuit('RC Low Pass Filter')
    
    # Add voltage sources
    circuit.VoltageSource('dc', 'vdd', circuit.gnd, 5@u_V)  # DC bias
    circuit.VoltageSource('ac', 'vin', circuit.gnd, 0@u_V)  # AC source
    
    # Add components
    circuit.R('1', 'vin', 'vout', 1@u_kΩ)    # Resistor 1kΩ
    circuit.C('1', 'vout', circuit.gnd, 1@u_uF)  # Capacitor 1μF
    circuit.R('load', 'vout', 'vdd', 10@u_kΩ)   # Load resistor for DC bias
    
    return circuit

# 1. Operating Point Analysis
def op_analysis(circuit, logger, output_dir):
    logger.log("=== Operating Point Analysis ===")
    simulator = circuit.simulator(temperature=25, nominal_temperature=25)
    analysis = simulator.operating_point()
    
    # 收集数据
    op_data = {}
    
    logger.log("Node voltages:")
    for node_name in analysis.nodes:
        node_value = analysis.nodes[node_name]
        voltage = float(node_value.as_ndarray()[0]) if hasattr(node_value, 'as_ndarray') else float(node_value)
        logger.log(f"  {node_name}: {voltage:.3f}V")
        op_data[f"Node_{node_name}_V"] = f"{voltage:.3f}"
    
    logger.log("Branch currents:")
    for branch_name in analysis.branches:
        branch_value = analysis.branches[branch_name]
        current = float(branch_value.as_ndarray()[0]) if hasattr(branch_value, 'as_ndarray') else float(branch_value)
        logger.log(f"  {branch_name}: {current*1000:.3f}mA")
        op_data[f"Branch_{branch_name}_mA"] = f"{current*1000:.3f}"
    
    # 保存数据
    logger.save_data("Operating Point Analysis", op_data)
    
    return analysis

# 2. DC Sweep Analysis
def dc_analysis(logger, output_dir):
    logger.log("\n=== DC Sweep Analysis ===")
    
    # Create circuit for DC sweep
    circuit = Circuit('RC Filter DC Sweep')
    circuit.VoltageSource('dc', 'vdd', circuit.gnd, 5@u_V)
    circuit.VoltageSource('vin', 'vin', circuit.gnd, 0@u_V)  # Will be swept
    circuit.R('1', 'vin', 'vout', 1@u_kΩ)
    circuit.C('1', 'vout', circuit.gnd, 1@u_uF)
    circuit.R('load', 'vout', 'vdd', 10@u_kΩ)
    
    simulator = circuit.simulator(temperature=25, nominal_temperature=25)
    
    # Sweep the input voltage from -2V to 2V
    analysis = simulator.dc(Vvin=slice(-2, 2, 0.1))
    
    # Extract values properly
    vin_values = np.array([float(v) for v in analysis.Vvin])
    vout_values = np.array([float(v) for v in analysis['vout']])
    
    # 保存数据到CSV
    dc_data = pd.DataFrame({
        'Input_Voltage_V': vin_values,
        'Output_Voltage_V': vout_values,
        'Gain': np.where(np.abs(vin_values) > 1e-6, vout_values/vin_values, 0)
    })
    dc_data.to_csv(os.path.join(output_dir, 'dc_sweep_data.csv'), index=False)
    
    # Plot results
    plt.figure(figsize=(10, 6))
    plt.subplot(2, 1, 1)
    plt.plot(vin_values, vout_values, 'b-', linewidth=2)
    plt.xlabel('Input Voltage (V)')
    plt.ylabel('Output Voltage (V)')
    plt.title('DC Transfer Characteristic')
    plt.grid(True)
    
    plt.subplot(2, 1, 2)
    # Calculate gain avoiding division by zero
    gain = np.where(np.abs(vin_values) > 1e-6, vout_values/vin_values, 0)
    plt.plot(vin_values, gain, 'r-', linewidth=2)
    plt.xlabel('Input Voltage (V)')
    plt.ylabel('Voltage Gain')
    plt.title('DC Voltage Gain')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'dc_analysis.png'), dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存
    
    # 记录统计数据
    dc_stats = {
        "Input_Range": f"{vin_values[0]:.1f}V to {vin_values[-1]:.1f}V",
        "Output_Range": f"{vout_values.min():.3f}V to {vout_values.max():.3f}V",
        "Max_Gain": f"{gain.max():.3f}",
        "Min_Gain": f"{gain.min():.3f}"
    }
    
    logger.log(f"DC analysis completed. Input range: {dc_stats['Input_Range']}")
    logger.log(f"Output range: {dc_stats['Output_Range']}")
    logger.save_data("DC Sweep Analysis Statistics", dc_stats)
    
    return analysis

# 3. Transient Analysis
def transient_analysis(logger, output_dir):
    logger.log("\n=== Transient Analysis ===")
    
    # Create circuit for transient analysis with pulse source
    circuit = Circuit('RC Filter Transient')
    circuit.VoltageSource('dc', 'vdd', circuit.gnd, 5@u_V)
    
    # Create pulse source using raw SPICE syntax
    circuit.raw_spice += 'VPULSE vin 0 PULSE(0 3 1m 0.1m 0.1m 5m 10m)\n'
    
    circuit.R('1', 'vin', 'vout', 1@u_kΩ)
    circuit.C('1', 'vout', circuit.gnd, 1@u_uF)
    circuit.R('load', 'vout', 'vdd', 10@u_kΩ)
    
    simulator = circuit.simulator(temperature=25, nominal_temperature=25)
    analysis = simulator.transient(step_time=0.1@u_ms, end_time=20@u_ms)
    
    # Extract values properly
    time_values = np.array([float(t) for t in analysis.time])
    vin_values = np.array([float(v) for v in analysis['vin']])
    vout_values = np.array([float(v) for v in analysis['vout']])
    
    # 保存数据到CSV
    tran_data = pd.DataFrame({
        'Time_ms': time_values * 1000,
        'Input_Voltage_V': vin_values,
        'Output_Voltage_V': vout_values
    })
    tran_data.to_csv(os.path.join(output_dir, 'transient_data.csv'), index=False)
    
    # Plot results
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.plot(time_values*1000, vin_values, 'b-', linewidth=2, label='Input')
    plt.plot(time_values*1000, vout_values, 'r-', linewidth=2, label='Output')
    plt.xlabel('Time (ms)')
    plt.ylabel('Voltage (V)')
    plt.title('Transient Response - Step Input')
    plt.legend()
    plt.grid(True)
    
    # Calculate rise time
    tran_stats = {}
    max_val = np.max(vout_values)
    if max_val > 0.1:  # Only if there's a reasonable signal
        output_90 = 0.9 * max_val
        output_10 = 0.1 * max_val
        
        idx_10 = np.where(vout_values >= output_10)[0]
        idx_90 = np.where(vout_values >= output_90)[0]
        
        if len(idx_10) > 0 and len(idx_90) > 0:
            time_10 = time_values[idx_10[0]]
            time_90 = time_values[idx_90[0]]
            rise_time = time_90 - time_10
            
            plt.subplot(2, 1, 2)
            plt.plot(time_values*1000, vout_values, 'g-', linewidth=2)
            plt.axhline(y=output_10, color='k', linestyle='--', alpha=0.7, label='10% Level')
            plt.axhline(y=output_90, color='k', linestyle='--', alpha=0.7, label='90% Level')
            plt.axvline(x=time_10*1000, color='r', linestyle='--', alpha=0.7)
            plt.axvline(x=time_90*1000, color='r', linestyle='--', alpha=0.7)
            plt.xlabel('Time (ms)')
            plt.ylabel('Output Voltage (V)')
            plt.title(f'Rise Time Analysis (Rise Time = {rise_time*1000:.2f} ms)')
            plt.legend()
            plt.grid(True)
            
            tran_stats = {
                "Rise_Time_ms": f"{rise_time*1000:.2f}",
                "Time_10_percent_ms": f"{time_10*1000:.2f}",
                "Time_90_percent_ms": f"{time_90*1000:.2f}",
                "Max_Output_V": f"{max_val:.3f}",
                "Level_10_percent_V": f"{output_10:.3f}",
                "Level_90_percent_V": f"{output_90:.3f}"
            }
            
            logger.log(f"Rise time (10% to 90%): {rise_time*1000:.2f} ms")
        else:
            plt.subplot(2, 1, 2)
            plt.plot(time_values*1000, vout_values, 'g-', linewidth=2)
            plt.xlabel('Time (ms)')
            plt.ylabel('Output Voltage (V)')
            plt.title('Output Voltage vs Time')
            plt.grid(True)
            
            tran_stats = {
                "Max_Output_V": f"{max_val:.3f}",
                "Note": "Rise time calculation not applicable"
            }
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'transient_analysis.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.save_data("Transient Analysis Statistics", tran_stats)
    
    return analysis

# 4. AC Analysis
def ac_analysis(logger, output_dir):
    logger.log("\n=== AC Analysis ===")
    
    # Create circuit specifically for AC analysis
    circuit = Circuit('RC Filter AC Analysis')
    circuit.VoltageSource('dc', 'vdd', circuit.gnd, 5@u_V)
    
    # Use raw SPICE for AC source
    circuit.raw_spice += 'VAC vin 0 DC 0 AC 1\n'
    
    circuit.R('1', 'vin', 'vout', 1@u_kΩ)
    circuit.C('1', 'vout', circuit.gnd, 1@u_uF)
    circuit.R('load', 'vout', 'vdd', 10@u_kΩ)
    
    simulator = circuit.simulator(temperature=25, nominal_temperature=25)
    
    # Frequency sweep from 1Hz to 100kHz
    analysis = simulator.ac(start_frequency=1@u_Hz, stop_frequency=100@u_kHz, 
                           number_of_points=50, variation='dec')
    
    # Extract values properly
    frequency = np.array([float(f) for f in analysis.frequency])
    vout_complex = np.array(analysis['vout'])
    
    # Calculate magnitude and phase
    magnitude_db = 20 * np.log10(np.abs(vout_complex))
    phase_deg = np.angle(vout_complex) * 180 / np.pi
    
    # 保存数据到CSV
    ac_data = pd.DataFrame({
        'Frequency_Hz': frequency,
        'Magnitude_dB': magnitude_db,
        'Phase_deg': phase_deg,
        'Magnitude_linear': np.abs(vout_complex)
    })
    ac_data.to_csv(os.path.join(output_dir, 'ac_analysis_data.csv'), index=False)
    
    # Find -3dB frequency
    ac_stats = {}
    cutoff_idx = np.where(magnitude_db <= -3)[0]
    if len(cutoff_idx) > 0:
        f_3db = frequency[cutoff_idx[0]]
        logger.log(f"-3dB cutoff frequency: {f_3db:.1f} Hz")
        ac_stats["Cutoff_Frequency_3dB_Hz"] = f"{f_3db:.1f}"
    
    # Plot Bode plot
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.semilogx(frequency, magnitude_db, 'b-', linewidth=2)
    plt.axhline(y=-3, color='r', linestyle='--', alpha=0.7, label='-3dB Line')
    if len(cutoff_idx) > 0:
        plt.axvline(x=f_3db, color='r', linestyle='--', alpha=0.7, label=f'fc = {f_3db:.1f} Hz')
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Magnitude (dB)')
    plt.title('Bode Plot - Magnitude Response')
    plt.grid(True, which="both", ls="-", alpha=0.3)
    plt.legend()
    
    plt.subplot(2, 1, 2)
    plt.semilogx(frequency, phase_deg, 'r-', linewidth=2)
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Phase (degrees)')
    plt.title('Bode Plot - Phase Response')
    plt.grid(True, which="both", ls="-", alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'ac_analysis.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # Calculate theoretical cutoff frequency
    R = 1000  # 1kΩ
    C = 1e-6  # 1μF
    f_theoretical = 1 / (2 * np.pi * R * C)
    logger.log(f"Theoretical cutoff frequency: {f_theoretical:.1f} Hz")
    
    ac_stats.update({
        "Theoretical_Cutoff_Hz": f"{f_theoretical:.1f}",
        "Frequency_Range_Hz": f"{frequency[0]:.1f} to {frequency[-1]:.1f}",
        "Max_Magnitude_dB": f"{magnitude_db.max():.2f}",
        "Min_Magnitude_dB": f"{magnitude_db.min():.2f}",
        "Phase_Range_deg": f"{phase_deg.min():.1f} to {phase_deg.max():.1f}"
    })
    
    logger.save_data("AC Analysis Statistics", ac_stats)
    
    return analysis

# Main execution
def main():
    # 设置输出目录
    output_dir = setup_output_directory()
    
    # 创建日志管理器
    logger = Logger(output_dir)
    
    logger.log("PySpice Circuit Simulation Demo")
    logger.log("==============================")
    
    logger.log("Circuit: RC Low Pass Filter")
    logger.log("Components: R=1kΩ, C=1μF, DC bias=5V")
    
    # 保存电路信息
    circuit_info = {
        "Circuit_Type": "RC Low Pass Filter",
        "Resistor_R1": "1kΩ",
        "Capacitor_C1": "1μF", 
        "Load_Resistor": "10kΩ",
        "DC_Bias": "5V",
        "Simulation_Date": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    logger.save_data("Circuit Configuration", circuit_info)
    
    # Run all analyses
    try:
        # 1. Operating Point
        circuit = create_rc_filter_circuit()
        op_result = op_analysis(circuit, logger, output_dir)
        
        # 2. DC Sweep
        dc_result = dc_analysis(logger, output_dir)
        
        # 3. Transient Analysis
        tran_result = transient_analysis(logger, output_dir)
        
        # 4. AC Analysis
        ac_result = ac_analysis(logger, output_dir)
        
        logger.log("\n=== Simulation Summary ===")
        logger.log("All simulations completed successfully!")
        logger.log("Generated files in output directory:")
        logger.log("- simulation_log.txt: Complete simulation log")
        logger.log("- simulation_data.txt: All simulation statistics")
        logger.log("- dc_analysis.png: DC transfer characteristics")
        logger.log("- transient_analysis.png: Step response and rise time")
        logger.log("- ac_analysis.png: Bode plot (magnitude and phase)")
        logger.log("- dc_sweep_data.csv: DC sweep raw data")
        logger.log("- transient_data.csv: Transient analysis raw data") 
        logger.log("- ac_analysis_data.csv: AC analysis raw data")
        
        # 生成文件列表
        files_created = []
        for file in os.listdir(output_dir):
            files_created.append(file)
        
        summary_data = {
            "Total_Files_Created": str(len(files_created)),
            "Files_List": ", ".join(sorted(files_created)),
            "Output_Directory": output_dir,
            "Simulation_Status": "Completed Successfully"
        }
        logger.save_data("Final Summary", summary_data)
        
        print(f"\nAll output files saved to: {os.path.abspath(output_dir)}")
        
    except Exception as e:
        logger.log(f"Error during simulation: {e}")
        import traceback
        error_details = traceback.format_exc()
        logger.log("Error details:")
        logger.log(error_details)
        
        # 保存错误信息
        error_data = {
            "Error_Message": str(e),
            "Error_Details": error_details,
            "Simulation_Status": "Failed"
        }
        logger.save_data("Error Report", error_data)

if __name__ == "__main__":
    main()