import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Any
from pydantic import BaseModel

# 电路模型相关类型定义
class CircuitComponent(BaseModel):
    name: str
    type: str
    nodes: List[str]
    value: float
    unit: str

class CircuitModel(BaseModel):
    title: str
    components: List[CircuitComponent]
    pyspice_circuit: Optional[Any] = None

class SimulationResult(BaseModel):
    analysis_type: str
    data: Optional[Any] = None
    vectors: Optional[List[str]] = None
    error: Optional[str] = None

class LoggingManager:
    """统一的日志和输出管理系统"""
    
    def __init__(self, session_dir: Path = None):
        # 基于程序启动时间创建输出目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.base_output_dir = Path("output")
        self.session_dir = session_dir if session_dir else self.base_output_dir / f"session_{self.timestamp}"
        
        # 创建子目录
        self.dirs = {
            'circuits': self.session_dir / "circuits",
            'simulations': self.session_dir / "simulations", 
            'results': self.session_dir / "results",
            'logs': self.session_dir / "logs"
        }
        
        self._create_directories()
        
        # 设置日志文件
        self.log_file = self.dirs['logs'] / "console_output.log"
        
        # 设置输出重定向
        self._setup_print_redirect()
    
    def _setup_print_redirect(self):
        """设置print输出同时写入终端和文件"""
        self.original_stdout = sys.stdout
        self.log_file_handle = open(self.log_file, 'w', encoding='utf-8')
        
        class TeeOutput:
            def __init__(self, stdout, log_file):
                self.stdout = stdout
                self.log_file = log_file
            
            def write(self, text):
                # 检测是否是JSON字符串，如果是则美化输出
                try:
                    # 尝试解析为JSON
                    if '{"' in text or '[\n' in text:
                        data = json.loads(text)
                        # 使用json.dumps进行美化输出，不使用转义字符
                        formatted_text = json.dumps(data, indent=2, ensure_ascii=False)
                        self.stdout.write(formatted_text + '\n')
                        self.log_file.write(formatted_text + '\n')
                        return
                except:
                    pass
                    
                # 如果不是JSON或解析失败，正常输出
                self.stdout.write(text)
                self.stdout.flush()
                self.log_file.write(text)
                self.log_file.flush()
            
            def flush(self):
                self.stdout.flush()
                self.log_file.flush()
        
        sys.stdout = TeeOutput(self.original_stdout, self.log_file_handle)
    
    def log_info(self, message: str):
        """记录信息"""
        print(f"ℹ️ {message}")
    
    def _create_directories(self):
        """创建所有必要的目录"""
        self.session_dir.mkdir(parents=True, exist_ok=True)
        for dir_path in self.dirs.values():
            dir_path.mkdir(exist_ok=True)

    def get_file_path(self, category: str, filename: str) -> Path:
        """获取特定类别的文件路径"""
        if category not in self.dirs:
            raise ValueError(f"Unknown category: {category}. Available: {list(self.dirs.keys())}")
        return self.dirs[category] / filename

    def save_circuit_model(self, circuit_model: 'CircuitModel', iteration: int) -> Path:
        """保存电路模型到文件"""
        filename = f"circuit_iter_{iteration:03d}.json"
        filepath = self.get_file_path('circuits', filename)
        
        circuit_data = {
            "title": circuit_model.title,
            "iteration": iteration,
            "timestamp": datetime.now().isoformat(),
            "components": [
                {
                    "name": comp.name,
                    "type": comp.type,
                    "nodes": list(comp.nodes),
                    "value": comp.value,
                    "unit": comp.unit
                }
                for comp in circuit_model.components
            ]
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(circuit_data, f, indent=2, ensure_ascii=False)
        
        self.log_info(f"Circuit model saved: {filepath}")
        return filepath

    def save_simulation_results(self, sim_results: 'SimulationResult', iteration: int) -> Path:
        """保存仿真结果到文件"""
        filename = f"simulation_iter_{iteration:03d}.json"
        filepath = self.get_file_path('simulations', filename)
        
        # Convert numpy arrays to lists for JSON serialization
        data = {}
        if sim_results.data:
            for key, value in sim_results.data.items():
                if hasattr(value, 'real') and hasattr(value, 'imag'):
                    # Handle complex arrays
                    data[key] = {
                        'real': value.real.tolist() if hasattr(value.real, 'tolist') else value.real,
                        'imag': value.imag.tolist() if hasattr(value.imag, 'tolist') else value.imag
                    }
                elif hasattr(value, 'tolist'):
                    data[key] = value.tolist()
                else:
                    data[key] = value
        
        result_data = {
            "iteration": iteration,
            "timestamp": datetime.now().isoformat(),
            "analysis_type": sim_results.analysis_type,
            "data": data,
            "vectors": sim_results.vectors,
            "error": sim_results.error
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        
        self.log_info(f"Simulation results saved: {filepath}")
        return filepath

    def save_final_results(self, final_state: dict) -> Path:
        """保存最终结果摘要"""
        filename = "final_results.json"
        filepath = self.get_file_path('results', filename)
        
        # Prepare serializable data
        results_summary = {
            "session_id": self.timestamp,
            "timestamp": datetime.now().isoformat(),
            "user_request": final_state.get('user_request', ''),
            "total_iterations": final_state.get('iteration_count', 0),
            "goal_achieved": False,
            "design_goal": None,
            "final_circuit": None,
            "performance_metrics": {},
            "critique_history": []
        }
        
        # Add design goal
        if final_state.get('design_goal'):
            goal = final_state['design_goal']
            results_summary["design_goal"] = {
                "circuit_type": goal.circuit_type,
                "target_metric": goal.target_metric,
                "target_value": goal.target_value,
                "target_unit": goal.target_unit,
                "tolerance": goal.tolerance
            }
        
        # Add final circuit
        if final_state.get('circuit_model'):
            circuit = final_state['circuit_model']
            results_summary["final_circuit"] = {
                "title": circuit.title,
                "components": [
                    {
                        "name": comp.name,
                        "type": comp.type,
                        "nodes": list(comp.nodes),
                        "value": comp.value,
                        "unit": comp.unit
                    }
                    for comp in circuit.components
                ]
            }
        
        # Add critique history
        if final_state.get('critique_history'):
            latest_critique = final_state['critique_history'][-1]
            results_summary["goal_achieved"] = latest_critique.goal_met
            results_summary["performance_metrics"] = latest_critique.metrics
            results_summary["critique_history"] = [
                {
                    "goal_met": critique.goal_met,
                    "feedback": critique.feedback,
                    "metrics": critique.metrics
                }
                for critique in final_state['critique_history']
            ]
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, indent=2, ensure_ascii=False)
        
        self.log_info(f"Final results saved: {filepath}")
        return filepath

    def log_error(self, error_msg: str, context: str = ""):
        """记录错误信息"""
        print(f"❌ Error in {context}: {error_msg}" if context else f"❌ Error: {error_msg}")

    def log_llm_interaction(self, node_name: str, interaction_data: dict):
        """记录LLM交互信息"""
        print(f"\n🤖 LLM交互 - {node_name}")
        print(f"{node_name} 输入:" + "*"*60)
        print(interaction_data.get("request", "") )
        print(f"\n{node_name} 响应:" + "*"*60)
        print(interaction_data.get("response", ""))

    # def log_node_execution(self, node_name: str, input_state: dict, output_state: dict, execution_time: float):
    #     """记录节点执行信息"""
    #     print(f"\n⚡ 节点执行 - {node_name} ({execution_time:.2f}s)")
    #     print("输入状态:")
    #     print(json.dumps({k: str(v) for k, v in input_state.items()}, indent=2, ensure_ascii=False))
    #     print("\n输出状态:")
    #     print(json.dumps({k: str(v) for k, v in output_state.items()}, indent=2, ensure_ascii=False))

    def close(self):
        """关闭日志文件"""
        if hasattr(self, 'log_file_handle'):
            self.log_file_handle.close()
        sys.stdout = self.original_stdout

def handle_error(error: Exception, context: str, exit_on_error: bool = False):
    """
    统一的错误处理函数
    
    Args:
        error: 发生的异常
        context: 错误发生的上下文描述
        exit_on_error: 是否在错误后退出程序
    """
    error_msg = f"Error in {context}: {str(error)}"
    print(f"❌ {error_msg}")
    
    if exit_on_error:
        # print(f"程序因严重错误退出: {context}")
        sys.exit(1)
